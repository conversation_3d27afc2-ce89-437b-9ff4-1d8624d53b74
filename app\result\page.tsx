'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import { RiasecScores } from '@/lib/types';
import { riasecDescriptions } from '@/lib/questions';
import { getProfileInterpretation, getScoreLevel, ProfileInterpretation } from '@/lib/profileStore';
import Radar<PERSON>hart from '@/components/RadarChart';
import ProfileSummary from '@/components/ProfileSummary';

export default function ResultPage() {
  const searchParams = useSearchParams();
  const [scores, setScores] = useState<RiasecScores | null>(null);
  const [profileInterpretation, setProfileInterpretation] = useState<ProfileInterpretation | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState<boolean>(false);
  const [isGenerating, setIsGenerating] = useState<boolean>(false); // Flag untuk prevent double execution

  // Fungsi async untuk generate profile interpretation dengan useCallback
  const generateProfileInterpretation = useCallback(async (scoresData: RiasecScores) => {
    // Prevent double execution
    if (isGenerating) {
      console.log('Profile generation already in progress, skipping...');
      return;
    }

    console.log('Starting profile generation for scores:', scoresData);
    setIsGenerating(true);
    setIsLoadingProfile(true);

    try {
      const interpretation = await getProfileInterpretation(scoresData);

      // Check if component is still mounted before setting state
      setProfileInterpretation(interpretation);
      console.log('Profile generation completed successfully');
    } catch (error) {
      console.error('Error generating profile interpretation:', error);
      setError('Gagal menghasilkan interpretasi profil. Silakan coba lagi.');
    } finally {
      setIsLoadingProfile(false);
      setIsGenerating(false);
    }
  }, [isGenerating]); // Dependency pada isGenerating

  useEffect(() => {
    // Reset states saat searchParams berubah
    setError(null);
    setProfileInterpretation(null);
    setIsGenerating(false);

    try {
      const r = parseInt(searchParams.get('r') || '0');
      const i = parseInt(searchParams.get('i') || '0');
      const a = parseInt(searchParams.get('a') || '0');
      const s = parseInt(searchParams.get('s') || '0');
      const e = parseInt(searchParams.get('e') || '0');
      const c = parseInt(searchParams.get('c') || '0');

      // Validate scores (each should be between 5-25 for 5 questions with 1-5 scale)
      if ([r, i, a, s, e, c].some(score => score < 5 || score > 25 || isNaN(score))) {
        setError('Data hasil tes tidak valid. Silakan ulangi tes.');
        return;
      }

      const scoresData = { R: r, I: i, A: a, S: s, E: e, C: c };

      // Cek apakah scores sudah berubah untuk menghindari duplicate generation
      setScores(prevScores => {
        const scoresChanged = !prevScores ||
          prevScores.R !== scoresData.R ||
          prevScores.I !== scoresData.I ||
          prevScores.A !== scoresData.A ||
          prevScores.S !== scoresData.S ||
          prevScores.E !== scoresData.E ||
          prevScores.C !== scoresData.C;

        if (scoresChanged) {
          // Generate profile interpretation using AI hanya jika scores berubah
          generateProfileInterpretation(scoresData);
        }

        return scoresData;
      });
    } catch (err) {
      setError('Terjadi kesalahan dalam memproses hasil tes.');
    }
  }, [searchParams]);

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center p-4">
        <div className="max-w-md mx-auto text-center bg-white rounded-2xl shadow-xl p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link 
            href="/"
            className="inline-block bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
          >
            Kembali ke Beranda
          </Link>
        </div>
      </div>
    );
  }

  if (!scores) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Memproses hasil tes...</p>
        </div>
      </div>
    );
  }

  // Find the highest scoring type(s)
  const maxScore = Math.max(...Object.values(scores));
  const dominantTypes = Object.entries(scores)
    .filter(([_, score]) => score === maxScore)
    .map(([type, _]) => type);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Hasil Tes Minat Karir RIASEC
          </h1>
          <p className="text-lg text-gray-600">
            Berikut adalah profil minat karir Anda berdasarkan model RIASEC
          </p>
        </div>

        {/* Profile Summary Section */}
        {isLoadingProfile ? (
          <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl shadow-xl p-8 mb-8 text-white">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <h2 className="text-2xl font-semibold mb-2">Menganalisis Profil Anda...</h2>
              <p className="text-indigo-100">
                AI sedang menganalisis skor RIASEC Anda untuk memberikan interpretasi yang personal dan akurat.
              </p>
            </div>
          </div>
        ) : profileInterpretation ? (
          <ProfileSummary profileInterpretation={profileInterpretation} />
        ) : null}

        {/* Chart Section */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
            Profil Minat Karir Anda
          </h2>
          <RadarChart scores={scores} />
        </div>

        {/* Scores Summary */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            Ringkasan Skor Detail
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(scores).map(([type, score]) => {
              const description = riasecDescriptions.find(d => d.type === type);
              const isHighest = dominantTypes.includes(type);
              const scoreLevel = getScoreLevel(score);

              return (
                <div
                  key={type}
                  className={`p-6 rounded-xl border-2 transition-all hover:shadow-lg ${
                    isHighest
                      ? 'border-indigo-500 bg-indigo-50 shadow-md'
                      : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center mb-4">
                    <div className={`text-3xl font-bold mb-2 ${
                      isHighest ? 'text-indigo-600' : 'text-gray-700'
                    }`}>
                      {score}
                    </div>
                    <div className={`text-sm font-semibold mb-2 ${
                      isHighest ? 'text-indigo-600' : 'text-gray-600'
                    }`}>
                      {description?.name} ({type})
                    </div>
                    <div className={`inline-block px-3 py-1 rounded-full text-xs font-medium border ${scoreLevel.color}`}>
                      {scoreLevel.level}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 text-center">
                    {scoreLevel.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Development Areas */}
        {profileInterpretation && profileInterpretation.developmentAreas.length > 0 && (
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
              <span className="mr-3">📈</span>
              Area Pengembangan
            </h2>
            <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
              <p className="text-amber-800 mb-4 font-medium">
                Berdasarkan hasil tes, berikut adalah area yang dapat Anda kembangkan untuk memperluas peluang karir:
              </p>
              <ul className="space-y-2">
                {profileInterpretation.developmentAreas.map((area, index) => (
                  <li key={index} className="flex items-start text-amber-700">
                    <span className="text-amber-500 mr-2 mt-1">•</span>
                    <span>{area}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* RIASEC Descriptions */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">
            Penjelasan Tipe RIASEC
          </h2>
          <div className="grid gap-6">
            {riasecDescriptions.map((desc) => {
              const score = scores[desc.type];
              const isHighest = dominantTypes.includes(desc.type);
              const scoreLevel = getScoreLevel(score);

              return (
                <div
                  key={desc.type}
                  className={`p-6 rounded-lg border-l-4 transition-all hover:shadow-md ${
                    isHighest
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-300 bg-gray-50'
                  }`}
                >
                  <div className="flex justify-between items-start mb-3">
                    <h3 className={`text-lg font-semibold ${
                      isHighest ? 'text-indigo-700' : 'text-gray-700'
                    }`}>
                      {desc.name} ({desc.type})
                    </h3>
                    <div className="flex flex-col items-end space-y-1">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        isHighest
                          ? 'bg-indigo-200 text-indigo-800'
                          : 'bg-gray-200 text-gray-700'
                      }`}>
                        Skor: {score}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs font-medium border ${scoreLevel.color}`}>
                        {scoreLevel.level}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    {desc.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>

        {/* Actions */}
        <div className="text-center">
          <div className="space-x-4">
            <Link 
              href="/"
              className="inline-block bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              Kembali ke Beranda
            </Link>
            <Link 
              href="/test"
              className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              Ulangi Tes
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
