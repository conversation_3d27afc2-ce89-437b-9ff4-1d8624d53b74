import { likertOptions } from '@/lib/questions';

interface LikertScaleInputProps {
  questionId: number;
  selectedValue?: number;
  onSelect: (questionId: number, value: number) => void;
}

export default function LikertScaleInput({ 
  questionId, 
  selectedValue, 
  onSelect 
}: LikertScaleInputProps) {
  return (
    <div className="space-y-3">
      <p className="text-center text-gray-600 mb-6">
        Seberapa setuju Anda dengan pernyataan di atas?
      </p>
      
      <div className="grid gap-3">
        {likertOptions.map((option) => (
          <button
            key={option.value}
            onClick={() => onSelect(questionId, option.value)}
            className={`
              w-full p-4 rounded-lg border-2 transition-all duration-200 text-left
              ${selectedValue === option.value
                ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                : 'border-gray-200 hover:border-indigo-300 hover:bg-gray-50'
              }
            `}
          >
            <div className="flex items-center">
              <div className={`
                w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center
                ${selectedValue === option.value
                  ? 'border-indigo-500 bg-indigo-500'
                  : 'border-gray-300'
                }
              `}>
                {selectedValue === option.value && (
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                )}
              </div>
              <span className="font-medium">{option.label}</span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}
