import { likertOptions } from '@/lib/questions';

interface LikertScaleInputProps {
  questionId: number;
  selectedValue?: number;
  onSelect: (questionId: number, value: number) => void;
}

export default function LikertScaleInput({
  questionId,
  selectedValue,
  onSelect
}: LikertScaleInputProps) {
  return (
    <div className="space-y-4">
      <p className="text-center text-gray-600 mb-6">
        Seberapa setuju Anda dengan pernyataan di atas?
      </p>

      <div className="space-y-3">
        {likertOptions.map((option) => (
          <label
            key={option.value}
            className="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          >
            <input
              type="radio"
              name={`question-${questionId}`}
              value={option.value}
              checked={selectedValue === option.value}
              onChange={() => onSelect(questionId, option.value)}
              className="w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500 focus:ring-2"
            />
            <span className="ml-3 text-gray-700 font-medium">
              {option.label}
            </span>
          </label>
        ))}
      </div>
    </div>
  );
}
