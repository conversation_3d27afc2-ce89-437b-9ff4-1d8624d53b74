import { ProfileInterpretation } from '@/lib/profileStore';

interface ProfileSummaryProps {
  profileInterpretation: ProfileInterpretation;
}

export default function ProfileSummary({ profileInterpretation }: ProfileSummaryProps) {
  return (
    <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl shadow-xl p-8 mb-8 text-white">
      <div className="text-center mb-6">
        <div className="inline-block bg-white/20 rounded-full p-4 mb-4">
          <span className="text-4xl">🎯</span>
        </div>
        <h2 className="text-3xl font-bold mb-2">
          {profileInterpretation.profileTitle}
        </h2>
        <p className="text-indigo-100 text-lg leading-relaxed max-w-3xl mx-auto">
          {profileInterpretation.profileDescription}
        </p>
      </div>
      
      <div className="grid md:grid-cols-2 gap-6 mb-6">
        {/* Strengths */}
        <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <span className="mr-2">💪</span>
            Kekuatan Utama
          </h3>
          <ul className="space-y-2">
            {profileInterpretation.strengths.map((strength, index) => (
              <li key={index} className="flex items-start">
                <span className="text-yellow-300 mr-2 mt-1">✓</span>
                <span className="text-indigo-100">{strength}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Career Suggestions */}
        <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            <span className="mr-2">🚀</span>
            Saran Karir
          </h3>
          <ul className="space-y-2">
            {profileInterpretation.careerSuggestions.slice(0, 5).map((career, index) => (
              <li key={index} className="flex items-start">
                <span className="text-yellow-300 mr-2 mt-1">•</span>
                <span className="text-indigo-100">{career}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Work Environment */}
      <div className="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
        <h3 className="text-xl font-semibold mb-3 flex items-center">
          <span className="mr-2">🏢</span>
          Lingkungan Kerja Ideal
        </h3>
        <p className="text-indigo-100 leading-relaxed">
          {profileInterpretation.workEnvironment}
        </p>
      </div>

      {/* Call to Action */}
      <div className="text-center mt-6">
        <div className="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
          <p className="text-indigo-100 text-sm">
            💡 <strong>Tips:</strong> Gunakan hasil ini sebagai panduan untuk eksplorasi karir. 
            Pertimbangkan untuk mencoba aktivitas atau pekerjaan yang sesuai dengan profil Anda!
          </p>
        </div>
      </div>
    </div>
  );
}
