'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getShuffledQuestions } from '@/lib/questions';
import { RiasecScores, Question } from '@/lib/types';
import ProgressBar from '@/components/ProgressBar';
import QuestionCard from '@/components/QuestionCard';
import LikertScaleInput from '@/components/LikertScaleInput';
import NavigationButtons from '@/components/NavigationButtons';

export default function TestPage() {
  const router = useRouter();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<{ [questionId: number]: number }>({});
  const [shuffledQuestions, setShuffledQuestions] = useState<Question[]>([]);

  // Initialize shuffled questions on component mount
  useEffect(() => {
    setShuffledQuestions(getShuffledQuestions());
  }, []);

  // Don't render until questions are shuffled
  if (shuffledQuestions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Mempersiapkan pertanyaan...</p>
        </div>
      </div>
    );
  }

  const currentQuestion = shuffledQuestions[currentQuestionIndex];
  const totalQuestions = shuffledQuestions.length;
  const hasAnsweredCurrent = answers[currentQuestion.id] !== undefined;

  const handleAnswerSelect = (questionId: number, value: number) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));

    // Auto-advance to next question after a short delay
    setTimeout(() => {
      if (currentQuestionIndex < totalQuestions - 1) {
        setCurrentQuestionIndex(prev => prev + 1);
      } else {
        // Last question answered, calculate results
        handleTestComplete(questionId, value);
      }
    }, 500);
  };

  const handleTestComplete = (lastQuestionId: number, lastValue: number) => {
    // Include the last answer in calculation
    const finalAnswers = {
      ...answers,
      [lastQuestionId]: lastValue
    };

    // Calculate RIASEC scores
    const scores: RiasecScores = {
      R: 0,
      I: 0,
      A: 0,
      S: 0,
      E: 0,
      C: 0
    };

    shuffledQuestions.forEach(question => {
      const answer = finalAnswers[question.id];
      if (answer !== undefined) {
        scores[question.riasec_type] += answer;
      }
    });

    // Navigate to result page with scores as query parameters
    const queryParams = new URLSearchParams({
      r: scores.R.toString(),
      i: scores.I.toString(),
      a: scores.A.toString(),
      s: scores.S.toString(),
      e: scores.E.toString(),
      c: scores.C.toString()
    });

    router.push(`/result?${queryParams.toString()}`);
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleNext = () => {
    if (hasAnsweredCurrent) {
      if (currentQuestionIndex < totalQuestions - 1) {
        setCurrentQuestionIndex(prev => prev + 1);
      } else {
        // Last question, complete test
        handleTestComplete(currentQuestion.id, answers[currentQuestion.id]);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Tes Minat Karir RIASEC
          </h1>
          <p className="text-gray-600">
            Jawab setiap pertanyaan sesuai dengan preferensi Anda
          </p>
        </div>

        <ProgressBar 
          current={currentQuestionIndex + 1} 
          total={totalQuestions} 
        />

        <QuestionCard 
          question={currentQuestion}
          questionNumber={currentQuestionIndex + 1}
        />

        <div className="bg-white rounded-xl shadow-lg p-8">
          <LikertScaleInput
            questionId={currentQuestion.id}
            selectedValue={answers[currentQuestion.id]}
            onSelect={handleAnswerSelect}
          />

          <NavigationButtons
            currentIndex={currentQuestionIndex}
            totalQuestions={totalQuestions}
            canGoNext={hasAnsweredCurrent}
            onPrevious={handlePrevious}
            onNext={handleNext}
          />
        </div>
      </div>
    </div>
  );
}
